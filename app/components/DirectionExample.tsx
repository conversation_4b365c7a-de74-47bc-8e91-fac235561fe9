/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { useRDSTheme, useDirection, rdsThemeStyles, modeStyles, directionStyles } from "~/theme";
import { DirectionToggle } from "./DirectionToggle";

export function DirectionExample() {
  const { theme } = useRDSTheme();
  const { direction, isRTL } = useDirection();
  const styles = rdsThemeStyles(theme);

  const containerStyle = css`
    padding: ${styles.spacing(6)};
    max-width: 42rem;
    margin: 0 auto;

    & > * + * {
      margin-top: ${styles.spacing(6)};
    }
  `;

  const headerStyle = css`
    display: flex;
    align-items: center;
    justify-content: space-between;
  `;

  const titleStyle = css`
    font-size: ${styles.fontSize('2xl')};
    font-weight: ${styles.fontWeight('bold')};
    color: ${modeStyles(theme, styles.colors.neutral(900), styles.colors.neutral(100))};
  `;

  return (
    <div css={containerStyle}>
      <div css={headerStyle}>
        <h2 css={titleStyle}>Direction Support Example</h2>
        <DirectionToggle />
      </div>

      {/* Status display */}
      <div css={css`
        background-color: ${modeStyles(theme, styles.colors.secondary(50), styles.colors.secondary(900))};
        padding: ${styles.spacing(4)};
        border-radius: ${styles.borderRadius('lg')};
      `}>
        <p css={css`
          font-size: ${styles.fontSize('sm')};
          color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
          margin-bottom: ${styles.spacing(2)};
        `}>
          Current direction: <strong>{direction.toUpperCase()}</strong>
        </p>
        <p css={css`
          font-size: ${styles.fontSize('sm')};
          color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
        `}>
          Is RTL: <strong>{isRTL ? 'Yes' : 'No'}</strong>
        </p>
      </div>

      {/* Direction-aware margin example */}
      <div>
        <h3 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin-bottom: ${styles.spacing(4)};
          color: ${modeStyles(theme, styles.colors.neutral(900), styles.colors.neutral(100))};
        `}>
          Direction-Aware Margins
        </h3>
        <div css={css`
          ${directionStyles(theme,
            `margin-left: ${styles.spacing(8)}; border-left: 4px solid ${styles.colors.primary(500)};`,
            `margin-right: ${styles.spacing(8)}; border-right: 4px solid ${styles.colors.primary(500)};`
          )}
          padding: ${styles.spacing(4)};
          background-color: ${modeStyles(theme, styles.colors.primary(50), `${styles.colors.primary(900)}33`)};
          border-radius: ${styles.borderRadius('lg')};
        `}>
          <p>This content has direction-aware margins and borders.</p>
          <p css={css`
            font-size: ${styles.fontSize('sm')};
            color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
            margin-top: ${styles.spacing(2)};
          `}>
            LTR: Left margin + left border | RTL: Right margin + right border
          </p>
        </div>
      </div>

      {/* Text alignment example */}
      <div>
        <h3 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin-bottom: ${styles.spacing(4)};
          color: ${modeStyles(theme, styles.colors.neutral(900), styles.colors.neutral(100))};
        `}>
          Text Alignment
        </h3>
        <div css={css`
          ${directionStyles(theme, 'text-align: left;', 'text-align: right;')}
          padding: ${styles.spacing(4)};
          background-color: ${modeStyles(theme, styles.colors.secondary(100), styles.colors.secondary(800))};
          border-radius: ${styles.borderRadius('lg')};
        `}>
          <p>This text aligns based on the current direction.</p>
          <p css={css`
            font-size: ${styles.fontSize('sm')};
            color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
            margin-top: ${styles.spacing(2)};
          `}>
            LTR: Left aligned | RTL: Right aligned
          </p>
        </div>
      </div>
    </div>
  );
}
