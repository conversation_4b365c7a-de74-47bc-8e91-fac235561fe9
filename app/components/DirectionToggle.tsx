/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { useRDSTheme, useDirection, rdsThemeStyles, modeStyles } from "~/theme";

export function DirectionToggle() {
  const { theme } = useRDSTheme();
  const { isRTL, toggleDirection } = useDirection();
  const styles = rdsThemeStyles(theme);

  const buttonStyle = css`
    padding: ${styles.spacing(2)};
    border-radius: ${styles.borderRadius('md')};
    background-color: ${modeStyles(theme, styles.colors.secondary(100), styles.colors.secondary(800))};
    color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: ${modeStyles(theme, styles.colors.secondary(200), styles.colors.secondary(700))};
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px ${styles.colors.primary(500)}40;
    }
  `;

  return (
    <button
      onClick={toggleDirection}
      css={buttonStyle}
      aria-label={`Switch to ${isRTL ? 'LTR' : 'RTL'} direction`}
      title={`Switch to ${isRTL ? 'Left-to-Right' : 'Right-to-Left'} direction`}
    >
      {isRTL ? (
        // LTR icon (arrow pointing right)
        <svg
          css={css`
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
          `}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 7l5 5m0 0l-5 5m5-5H6"
          />
        </svg>
      ) : (
        // RTL icon (arrow pointing left)
        <svg
          css={css`
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
          `}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 17l-5-5m0 0l5-5m-5 5h12"
          />
        </svg>
      )}
    </button>
  );
}
