/** @jsxImportSource @emotion/react */
import { css, useTheme } from '@emotion/react';
import { rdsThemeStyles, modeStyles, rdsButtonStyles, ExtendedThemeConfig } from "~/theme";
import { ThemeToggle } from "./ThemeToggle";
import { DirectionToggle } from "./DirectionToggle";

export function EmotionExample() {
  const theme = useTheme() as ExtendedThemeConfig;
  const isDark = theme.mode === 'dark';
  const isRTL = theme.direction === 'rtl';
  const styles = rdsThemeStyles(theme);
  const buttons = rdsButtonStyles(theme);

  const containerStyle = css`
    padding: ${styles.spacing(6)};
    max-width: 42rem;
    margin: 0 auto;
    background-color: ${modeStyles(theme, 'white', styles.colors.neutral(900))};
    color: ${modeStyles(theme, styles.colors.neutral(900), styles.colors.neutral(100))};
    min-height: 100vh;

    & > * + * {
      margin-top: ${styles.spacing(6)};
    }
  `;

  const headerStyle = css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: ${styles.spacing(4)};
  `;

  const titleStyle = css`
    font-size: ${styles.fontSize('2xl')};
    font-weight: ${styles.fontWeight('bold')};
    color: ${styles.colors.primary(600)};
    margin: 0;
  `;

  const cardStyle = css`
    background-color: ${modeStyles(theme, styles.colors.secondary(50), styles.colors.secondary(800))};
    padding: ${styles.spacing(4)};
    border-radius: ${styles.borderRadius('lg')};
    border: 1px solid ${modeStyles(theme, styles.colors.secondary(200), styles.colors.secondary(700))};
  `;

  const statusStyle = css`
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: ${styles.spacing(4)};
    font-size: ${styles.fontSize('sm')};

    strong {
      color: ${styles.colors.primary(600)};
    }
  `;

  const buttonGroupStyle = css`
    display: flex;
    gap: ${styles.spacing(3)};
    flex-wrap: wrap;
  `;

  const demoButtonStyle = css`
    ${buttons.base};
    ${buttons.sizes.md};
    ${buttons.variants.primary};
  `;

  const secondaryButtonStyle = css`
    ${buttons.base};
    ${buttons.sizes.md};
    ${buttons.variants.secondary};
  `;

  const outlineButtonStyle = css`
    ${buttons.base};
    ${buttons.sizes.sm};
    ${buttons.variants.outline};
  `;

  return (
    <div css={containerStyle}>
      <header css={headerStyle}>
        <h1 css={titleStyle}>Emotion CSS + RDS Theme</h1>
        <div css={buttonGroupStyle}>
          <ThemeToggle />
          <DirectionToggle />
        </div>
      </header>

      <div css={cardStyle}>
        <h2 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin: 0 0 ${styles.spacing(3)} 0;
          color: ${modeStyles(theme, styles.colors.neutral(800), styles.colors.neutral(200))};
        `}>
          Theme Status
        </h2>
        <div css={statusStyle}>
          <div>Theme Mode: <strong>{isDark ? 'Dark' : 'Light'}</strong></div>
          <div>Direction: <strong>{isRTL ? 'RTL' : 'LTR'}</strong></div>
        </div>
      </div>

      <div css={cardStyle}>
        <h2 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin: 0 0 ${styles.spacing(4)} 0;
          color: ${modeStyles(theme, styles.colors.neutral(800), styles.colors.neutral(200))};
        `}>
          Button Examples
        </h2>
        <div css={buttonGroupStyle}>
          <button css={demoButtonStyle}>
            Primary Button
          </button>
          <button css={secondaryButtonStyle}>
            Secondary Button
          </button>
          <button css={outlineButtonStyle}>
            Outline Button
          </button>
        </div>
      </div>

      <div css={cardStyle}>
        <h2 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin: 0 0 ${styles.spacing(4)} 0;
          color: ${modeStyles(theme, styles.colors.neutral(800), styles.colors.neutral(200))};
        `}>
          Color Palette
        </h2>
        <div css={css`
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: ${styles.spacing(3)};
        `}>
          {(['primary', 'secondary', 'success', 'warning', 'error'] as const).map((colorName) => (
            <div key={colorName} css={css`
              text-align: center;
            `}>
              <div css={css`
                width: 100%;
                height: 60px;
                background-color: ${styles.colors[colorName](500)};
                border-radius: ${styles.borderRadius('md')};
                margin-bottom: ${styles.spacing(2)};
                border: 1px solid ${modeStyles(theme, styles.colors.secondary(300), styles.colors.secondary(600))};
              `} />
              <div css={css`
                font-size: ${styles.fontSize('sm')};
                font-weight: ${styles.fontWeight('medium')};
                text-transform: capitalize;
                color: ${modeStyles(theme, styles.colors.neutral(700), styles.colors.neutral(300))};
              `}>
                {colorName}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div css={cardStyle}>
        <h2 css={css`
          font-size: ${styles.fontSize('lg')};
          font-weight: ${styles.fontWeight('semibold')};
          margin: 0 0 ${styles.spacing(4)} 0;
          color: ${modeStyles(theme, styles.colors.neutral(800), styles.colors.neutral(200))};
        `}>
          Typography Scale
        </h2>
        <div css={css`
          & > * + * {
            margin-top: ${styles.spacing(3)};
          }
        `}>
          {(['xs', 'sm', 'base', 'lg', 'xl', '2xl'] as const).map((size) => (
            <div key={size} css={css`
              font-size: ${styles.fontSize(size)};
              color: ${modeStyles(theme, styles.colors.neutral(700), styles.colors.neutral(300))};
            `}>
              Font size: {size} - The quick brown fox jumps over the lazy dog
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
