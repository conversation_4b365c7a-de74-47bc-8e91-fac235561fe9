/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { useRDSTheme, rdsButtonStyles } from "~/theme";
import { forwardRef } from 'react';

export interface RDSButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  children: React.ReactNode;
}

export const RDSButton = forwardRef<HTMLButtonElement | HTMLAnchorElement, RDSButtonProps>(
  ({ variant = 'primary', size = 'md', href, children, className, ...props }, ref) => {
    const { theme } = useRDSTheme();
    const buttonStyles = rdsButtonStyles(theme);

    const combinedStyles = css`
      ${buttonStyles.base};
      ${buttonStyles.sizes[size]};
      ${buttonStyles.variants[variant]};
    `;

    if (href) {
      return (
        <a
          ref={ref as React.Ref<HTMLAnchorElement>}
          href={href}
          css={combinedStyles}
          className={className}
          {...(props as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
        >
          {children}
        </a>
      );
    }

    return (
      <button
        ref={ref as React.Ref<HTMLButtonElement>}
        css={combinedStyles}
        className={className}
        {...props}
      >
        {children}
      </button>
    );
  }
);

RDSButton.displayName = 'RDSButton';
