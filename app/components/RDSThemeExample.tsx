/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { useRDSTheme, useThemeMode, rdsThemeStyles, modeStyles } from "~/theme";

export function RDSThemeExample() {
  const { theme } = useRDSTheme();
  const { isDark, toggleMode } = useThemeMode();
  const styles = rdsThemeStyles(theme);

  const containerStyle = css`
    padding: 1.5rem;
    max-width: 42rem;
    margin: 0 auto;
  `;

  const titleStyle = css`
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: ${modeStyles(theme, '#1f2937', '#f9fafb')};
  `;

  const buttonStyle = css`
    padding: 0.5rem 1rem;
    background-color: ${styles.colors.primary()};
    color: white;
    border: none;
    border-radius: ${styles.borderRadius('md')};
    cursor: pointer;
    margin-bottom: 1.5rem;

    &:hover {
      background-color: ${styles.colors.primary('600')};
    }
  `;

  return (
    <div css={containerStyle}>
      <h2 css={titleStyle}>RDS Theme Integration Example</h2>

      <button css={buttonStyle} onClick={toggleMode}>
        Switch to {isDark ? 'Light' : 'Dark'} Mode
      </button>

      <div css={css`
        background-color: ${modeStyles(theme, 'white', '#374151')};
        padding: 1rem;
        border-radius: ${styles.borderRadius('lg')};
        margin-bottom: 1rem;
      `}>
        <h3 css={css`
          font-size: 1.125rem;
          font-weight: 600;
          margin-bottom: 0.75rem;
          color: ${modeStyles(theme, '#374151', '#f3f4f6')};
        `}>
          Theme Information
        </h3>
        <div css={css`
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
          font-size: 0.875rem;
        `}>
          <div>
            <strong>Mode:</strong> {isDark ? 'Dark' : 'Light'}
          </div>
          <div>
            <strong>Direction:</strong> {styles.direction}
          </div>
        </div>
      </div>

      <div css={css`
        background-color: ${modeStyles(theme, '#f9fafb', '#1f2937')};
        padding: 1rem;
        border-radius: ${styles.borderRadius('md')};
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.75rem;
        color: ${modeStyles(theme, '#374151', '#d1d5db')};
        overflow-x: auto;
      `}>
        <h4 css={css`margin-bottom: 0.5rem; font-weight: 600;`}>
          RDS Theme Configuration:
        </h4>
        <pre>
          {JSON.stringify({
            mode: theme.mode,
            direction: theme.direction,
            rdsTheme: 'RoshnTheme object from @roshn/ui-kit'
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}
