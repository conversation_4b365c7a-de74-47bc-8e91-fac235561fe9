/** @jsxImportSource @emotion/react */
import { css, useTheme } from '@emotion/react';
import { rdsThemeStyles, modeStyles, ExtendedThemeConfig } from "~/theme";

export function ThemeToggle() {
  const theme = useTheme() as ExtendedThemeConfig;
  const isDark = theme.mode === 'dark';
  const styles = rdsThemeStyles(theme);

  // For now, we'll just show the current mode without toggle functionality
  // since we don't have a way to update the theme from components yet

  const buttonStyle = css`
    padding: ${styles.spacing(2)};
    border-radius: ${styles.borderRadius('md')};
    background-color: ${modeStyles(theme, styles.colors.secondary(100), styles.colors.secondary(800))};
    color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: ${modeStyles(theme, styles.colors.secondary(200), styles.colors.secondary(700))};
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px ${styles.colors.primary(500)}40;
    }
  `;

  return (
    <button
      onClick={() => {
        // TODO: Implement theme toggle functionality
        console.log('Theme toggle clicked');
      }}
      css={buttonStyle}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      {isDark ? (
        // Sun icon for light mode
        <svg
          css={css`
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
          `}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      ) : (
        // Moon icon for dark mode
        <svg
          css={css`
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
          `}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
      )}
    </button>
  );
}
