/** @jsxImportSource @emotion/react */
import { css, useTheme } from '@emotion/react';
import { useEffect, useRef, useState } from "react";
import { ThemeToggle } from "~/components/ThemeToggle";
import { DirectionToggle } from "~/components/DirectionToggle";
import { RDSButton } from "@roshn/ui-kit";
import { rdsThemeStyles, modeStyles, directionStyles, animations, breakpoints, ExtendedThemeConfig } from "~/theme";

interface Feature {
  title: string;
  description: string;
  icon: JSX.Element;
}

// Custom hook for intersection observer animations
function useIntersectionObserver<T extends HTMLElement = HTMLElement>(options = {}) {
  const elementRef = useRef<T | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
        // Once the element is visible, we can stop observing it
        if (elementRef.current) {
          observer.unobserve(elementRef.current);
        }
      }
    }, options);

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [options]);

  return { ref: elementRef, isVisible };
}

// Custom hook for sticky navbar
function useStickyNav() {
  const [isSticky, setIsSticky] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const lastScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Make the navbar sticky after scrolling down 100px
      if (currentScrollY > 100) {
        setIsSticky(true);

        // Hide navbar when scrolling down, show when scrolling up
        if (currentScrollY > lastScrollY.current) {
          setIsVisible(false);
        } else {
          setIsVisible(true);
        }
      } else {
        setIsSticky(false);
      }

      lastScrollY.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return { isSticky, isVisible };
}

const iconStyle = css`
  width: 1.5rem;
  height: 1.5rem;
`;

const features: Feature[] = [
  {
    title: "Modern Stack",
    description: "Built with Remix, React, and Emotion CSS for a modern development experience.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
  },
  {
    title: "Responsive Design",
    description: "Fully responsive design that looks great on all devices, from mobile to desktop.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    ),
  },
  {
    title: "Dark Mode & RTL",
    description: "Built-in dark mode and RTL direction support that automatically adapts to user preferences.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
      </svg>
    ),
  },
  {
    title: "TypeScript",
    description: "Full TypeScript support for a type-safe development experience.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    ),
  },
  {
    title: "Fast Performance",
    description: "Optimized for performance with server-side rendering and client-side hydration.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
  },
  {
    title: "RDS Theme System",
    description: "Integrated with Roshn Design System for consistent and beautiful UI components.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" css={iconStyle} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
      </svg>
    ),
  },
];

export default function LandingPage() {
  const theme = useTheme() as ExtendedThemeConfig;
  const styles = rdsThemeStyles(theme);
  const { isSticky, isVisible } = useStickyNav();
  const hero = useIntersectionObserver<HTMLElement>({ threshold: 0.75 });
  const heroText = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
  const heroButtons = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

  // Main container styles
  const containerStyle = css`
    min-height: 100vh;
    background: ${modeStyles(theme,
      'linear-gradient(to bottom, #ffffff, #f3f4f6)',
      'linear-gradient(to bottom, #111827, #030712)'
    )};
  `;

  // Container wrapper for consistent spacing
  const sectionContainer = css`
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
  `;

  return (
    <div css={containerStyle}>
      {/* Navigation */}
      <nav css={css`
        ${sectionContainer};
        padding-top: 1rem;
        padding-bottom: 1rem;
        transition: all 0.3s ease;
        ${isSticky ? css`
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background-color: ${modeStyles(theme, 'rgba(255, 255, 255, 0.95)', 'rgba(17, 24, 39, 0.95)')};
          backdrop-filter: blur(10px);
          z-index: 50;
          transform: translateY(${isVisible ? '0' : '-100%'});
        ` : ''}
      `}>
        <div css={css`
          display: flex;
          align-items: center;
          justify-content: space-between;
        `}>
          <div css={css`
            display: flex;
            align-items: center;
          `}>
            <div css={css`
              height: 2.5rem;
              width: 2.5rem;
            `}>
              <img
                src="/logo-light.png"
                alt="Logo"
                css={css`
                  display: ${modeStyles(theme, 'block', 'none')};
                  height: 100%;
                  width: auto;
                `}
              />
              <img
                src="/logo-dark.png"
                alt="Logo"
                css={css`
                  display: ${modeStyles(theme, 'none', 'block')};
                  height: 100%;
                  width: auto;
                `}
              />
            </div>
            <span css={css`
              margin-left: 0.75rem;
              font-size: 1.25rem;
              font-weight: bold;
              color: ${modeStyles(theme, styles.colors.neutral(800), 'white')};
            `}>
              AppName
            </span>
          </div>
          <div css={css`
            display: none;
            ${breakpoints.md} {
              display: block;
            }
          `}>
            <div css={css`
              display: flex;
              align-items: center;
              gap: 2rem;
            `}>
              <a href="#features" css={css`
                color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(300))};
                text-decoration: none;
                transition: color 0.2s ease;
                &:hover {
                  color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                }
              `}>
                Features
              </a>
              <a href="#testimonials" css={css`
                color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(300))};
                text-decoration: none;
                transition: color 0.2s ease;
                &:hover {
                  color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                }
              `}>
                Testimonials
              </a>
              <a href="#contact" css={css`
                color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(300))};
                text-decoration: none;
                transition: color 0.2s ease;
                &:hover {
                  color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                }
              `}>
                Contact
              </a>
              <ThemeToggle />
              <DirectionToggle />
              <RDSButton variant='primary' size="sm" text="Get Started" theme={theme.rdsTheme} />
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={hero.ref} css={css`
        ${sectionContainer};
        padding-top: 4rem;
        padding-bottom: 4rem;
        text-align: center;
        ${breakpoints.md} {
          padding-top: 6rem;
          padding-bottom: 6rem;
        }
      `}>
        <h1
          ref={heroText.ref}
          css={css`
            margin-bottom: 1.5rem;
            font-size: 2.25rem;
            font-weight: 800;
            line-height: 1.1;
            color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
            ${animations.fadeIn};
            ${heroText.isVisible ? '&.visible { opacity: 1; }' : ''}

            ${breakpoints.md} {
              font-size: 3rem;
            }

            ${breakpoints.lg} {
              font-size: 3.75rem;
            }
          `}
          className={heroText.isVisible ? 'visible' : ''}
        >
          Build Amazing Web Applications{' '}
          <span css={css`color: ${styles.colors.primary()};`}>
            Faster
          </span>
        </h1>
        <p css={css`
          margin: 0 auto 2.5rem auto;
          max-width: 42rem;
          font-size: 1.125rem;
          color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(300))};
          ${animations.slideUp};
          animation-delay: 200ms;
          ${heroText.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}
        `} className={heroText.isVisible ? 'visible' : ''}>
          A modern, responsive boilerplate with everything you need to start your next web project.
          Built with Remix, React, Emotion CSS, and RDS Theme System.
        </p>
        <div
          ref={heroButtons.ref}
          css={css`
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            ${animations.slideUp};
            animation-delay: 400ms;
            ${heroButtons.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}

            ${breakpoints.sm} {
              flex-direction: row;
              gap: 1rem;
            }
          `}
          className={heroButtons.isVisible ? 'visible' : ''}
        >
          <RDSButton variant='primary' size="lg" text="Get Started" theme={theme.rdsTheme} css={css`
            transition: transform 0.2s ease;
            &:hover {
              transform: scale(1.05);
            }
          `} />

          <RDSButton variant='primary' size="lg" text="Learn More" theme={theme.rdsTheme} css={css`
            transition: transform 0.2s ease;
            &:hover {
              transform: scale(1.05);
            }
          `} />
        </div>
      </section>

      {/* Features Section */}
      <section id="features" css={css`
        background-color: ${modeStyles(theme, 'white', styles.colors.secondary(800))};
        padding-top: 4rem;
        padding-bottom: 4rem;
        ${breakpoints.md} {
          padding-top: 6rem;
          padding-bottom: 6rem;
        }
      `}>
        <div css={sectionContainer}>
          {(() => {
            const featuresTitle = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
            const featuresGrid = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

            return (
              <>
                <h2
                  ref={featuresTitle.ref}
                  css={css`
                    margin-bottom: 3rem;
                    text-align: center;
                    font-size: 1.875rem;
                    font-weight: bold;
                    color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                    ${animations.slideUp};
                    ${featuresTitle.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}

                    ${breakpoints.md} {
                      font-size: 2.25rem;
                    }
                  `}
                  className={featuresTitle.isVisible ? 'visible' : ''}
                >
                  Key Features
                </h2>
                <div
                  ref={featuresGrid.ref}
                  css={css`
                    display: grid;
                    gap: 2rem;
                    ${featuresGrid.isVisible ? '&.visible { opacity: 1; }' : ''}

                    ${breakpoints.md} {
                      grid-template-columns: repeat(2, 1fr);
                    }

                    ${breakpoints.lg} {
                      grid-template-columns: repeat(3, 1fr);
                    }
                  `}
                  className={featuresGrid.isVisible ? 'visible' : ''}
                >
                  {features.map((feature, index) => {
                    const featureCard = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });
                    return (
                      <div
                        key={feature.title}
                        ref={featureCard.ref}
                        css={css`
                          border-radius: ${styles.borderRadius('lg')};
                          border: 1px solid ${modeStyles(theme, styles.colors.secondary(200), styles.colors.secondary(700))};
                          background-color: ${modeStyles(theme, 'white', styles.colors.secondary(900))};
                          padding: 1.5rem;
                          box-shadow: ${modeStyles(theme, '0 1px 3px 0 rgba(0, 0, 0, 0.1)', '0 4px 6px -1px rgba(0, 0, 0, 0.1)')};
                          ${animations.slideUp};
                          animation-delay: ${index * 100}ms;
                          ${featureCard.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}
                          transition: transform 0.2s ease, box-shadow 0.2s ease;

                          &:hover {
                            transform: translateY(-4px);
                            box-shadow: ${modeStyles(theme, '0 10px 25px -3px rgba(0, 0, 0, 0.1)', '0 20px 25px -5px rgba(0, 0, 0, 0.2)')};
                          }
                        `}
                        className={featureCard.isVisible ? 'visible' : ''}
                      >
                        <div css={css`
                          margin-bottom: 1rem;
                          display: inline-flex;
                          height: 3rem;
                          width: 3rem;
                          align-items: center;
                          justify-content: center;
                          border-radius: 50%;
                          background-color: ${modeStyles(theme, styles.colors.primary(100), styles.colors.primary(900))};
                          color: ${modeStyles(theme, styles.colors.primary(600), styles.colors.primary(300))};
                        `}>
                          {feature.icon}
                        </div>
                        <h3 css={css`
                          margin-bottom: 0.5rem;
                          font-size: 1.25rem;
                          font-weight: bold;
                          color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                        `}>
                          {feature.title}
                        </h3>
                        <p css={css`
                          color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
                          line-height: 1.6;
                        `}>
                          {feature.description}
                        </p>
                      </div>
                    );
                  })}
                </div>
              </>
            );
          })()}
        </div>
      </section>

      {/* CTA Section */}
      <section css={css`
        background-color: ${modeStyles(theme, styles.colors.primary(600), styles.colors.primary(800))};
        padding-top: 4rem;
        padding-bottom: 4rem;
        ${breakpoints.md} {
          padding-top: 6rem;
          padding-bottom: 6rem;
        }
      `}>
        <div css={css`
          ${sectionContainer};
          text-align: center;
        `}>
          {(() => {
            const ctaTitle = useIntersectionObserver<HTMLHeadingElement>({ threshold: 0.75 });
            const ctaText = useIntersectionObserver<HTMLParagraphElement>({ threshold: 0.75 });
            const ctaButton = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

            return (
              <>
                <h2
                  ref={ctaTitle.ref}
                  css={css`
                    margin-bottom: 1.5rem;
                    font-size: 1.875rem;
                    font-weight: bold;
                    color: white;
                    ${animations.slideUp};
                    ${ctaTitle.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}

                    ${breakpoints.md} {
                      font-size: 2.25rem;
                    }
                  `}
                  className={ctaTitle.isVisible ? 'visible' : ''}
                >
                  Ready to get started?
                </h2>
                <p
                  ref={ctaText.ref}
                  css={css`
                    margin: 0 auto 2rem auto;
                    max-width: 42rem;
                    font-size: 1.125rem;
                    color: ${styles.colors.primary(100)};
                    ${animations.slideUp};
                    animation-delay: 200ms;
                    ${ctaText.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}
                  `}
                  className={ctaText.isVisible ? 'visible' : ''}
                >
                  Join thousands of developers who are already building amazing applications with our RDS-powered boilerplate.
                </p>
                <div
                  ref={ctaButton.ref}
                  css={css`
                    ${animations.slideUp};
                    animation-delay: 400ms;
                    ${ctaButton.isVisible ? '&.visible { opacity: 1; transform: translateY(0); }' : ''}
                  `}
                  className={ctaButton.isVisible ? 'visible' : ''}
                >
                  <RDSButton
                    variant="secondary"
                    size="lg"
                    text="Start Building Now"
                    theme={theme.rdsTheme}
                    css={css`
                      background-color: white;
                      color: ${styles.colors.primary(600)};
                      transition: all 0.2s ease;

                      &:hover {
                        background-color: ${styles.colors.secondary(100)};
                        transform: scale(1.05);
                      }
                    `}
                  />
                </div>
              </>
            );
          })()}
        </div>
      </section>

      {/* Footer */}
      <footer css={css`
        background-color: ${modeStyles(theme, styles.colors.secondary(100), styles.colors.secondary(900))};
        padding-top: 3rem;
        padding-bottom: 3rem;
      `}>
        <div css={sectionContainer}>
          {(() => {
            const footerContent = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });
            const footerCopyright = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });

            return (
              <>
                <div
                  ref={footerContent.ref}
                  css={css`
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: space-between;
                    ${animations.fadeIn};
                    ${footerContent.isVisible ? '&.visible { opacity: 1; }' : ''}

                    ${breakpoints.md} {
                      flex-direction: row;
                    }
                  `}
                  className={footerContent.isVisible ? 'visible' : ''}
                >
                  <div css={css`
                    margin-bottom: 1.5rem;
                    display: flex;
                    align-items: center;

                    ${breakpoints.md} {
                      margin-bottom: 0;
                    }
                  `}>
                    <div css={css`
                      height: 2rem;
                      width: 2rem;
                    `}>
                      <img
                        src="/logo-light.png"
                        alt="Logo"
                        css={css`
                          display: ${modeStyles(theme, 'block', 'none')};
                          height: 100%;
                          width: auto;
                        `}
                      />
                      <img
                        src="/logo-dark.png"
                        alt="Logo"
                        css={css`
                          display: ${modeStyles(theme, 'none', 'block')};
                          height: 100%;
                          width: auto;
                        `}
                      />
                    </div>
                    <span css={css`
                      margin-left: 0.5rem;
                      font-size: 1.125rem;
                      font-weight: bold;
                      color: ${modeStyles(theme, styles.colors.secondary(800), 'white')};
                    `}>
                      AppName
                    </span>
                  </div>
                  <div css={css`
                    display: flex;
                    gap: 1.5rem;
                  `}>
                    <a href="#" css={css`
                      color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
                      transition: all 0.2s ease;

                      &:hover {
                        color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                        transform: scale(1.1);
                      }
                    `}>
                      <span css={css`
                        position: absolute;
                        width: 1px;
                        height: 1px;
                        padding: 0;
                        margin: -1px;
                        overflow: hidden;
                        clip: rect(0, 0, 0, 0);
                        white-space: nowrap;
                        border: 0;
                      `}>
                        GitHub
                      </span>
                      <svg css={css`height: 1.5rem; width: 1.5rem;`} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                      </svg>
                    </a>
                    <a href="#" css={css`
                      color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
                      transition: all 0.2s ease;

                      &:hover {
                        color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
                        transform: scale(1.1);
                      }
                    `}>
                      <span css={css`
                        position: absolute;
                        width: 1px;
                        height: 1px;
                        padding: 0;
                        margin: -1px;
                        overflow: hidden;
                        clip: rect(0, 0, 0, 0);
                        white-space: nowrap;
                        border: 0;
                      `}>
                        Twitter
                      </span>
                      <svg css={css`height: 1.5rem; width: 1.5rem;`} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                  </div>
                </div>
                <div
                  ref={footerCopyright.ref}
                  css={css`
                    margin-top: 2rem;
                    border-top: 1px solid ${modeStyles(theme, styles.colors.secondary(200), styles.colors.secondary(700))};
                    padding-top: 2rem;
                    text-align: center;
                    ${animations.fadeIn};
                    animation-delay: 300ms;
                    ${footerCopyright.isVisible ? '&.visible { opacity: 1; }' : ''}
                  `}
                  className={footerCopyright.isVisible ? 'visible' : ''}
                >
                  <p css={css`
                    color: ${modeStyles(theme, styles.colors.secondary(600), styles.colors.secondary(400))};
                  `}>
                    © 2024 AppName. All rights reserved. Built with RDS Theme System.
                  </p>
                </div>
              </>
            );
          })()}
        </div>
      </footer>
    </div>
  );
}