import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rollRestoration,
} from "@remix-run/react";
import type { LinksFunction } from "@remix-run/node";
import { ThemeProvider as EmotionThemeProvider } from "@emotion/react";
import { getThemeBare } from "@roshn/ui-kit";

import "./styles/global.css";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

// Create the RDS theme with default configuration
const rdsTheme = getThemeBare({
  direction: "ltr",
  name: "light",
  locale: "en",
  device: "desktop",
});

// Extended theme configuration with direction support
const themeConfig = {
  rdsTheme,
  mode: "light" as const,
  direction: "ltr" as const,
};

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" dir={themeConfig.direction}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body data-theme={themeConfig.mode} data-direction={themeConfig.direction}>
        <EmotionThemeProvider theme={themeConfig}>
            {children}
        </EmotionThemeProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}
