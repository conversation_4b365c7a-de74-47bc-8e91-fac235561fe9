import React, { createContext, useContext, useState, useEffect } from 'react';
import { useTheme as useEmotionTheme } from '@emotion/react';
import type { RoshnTheme } from '@roshn/ui-kit';

// Extended theme type that includes our additions
export interface ExtendedThemeConfig {
  rdsTheme: RoshnTheme;
  mode: 'light' | 'dark';
  direction: 'ltr' | 'rtl';
}

interface RDSThemeContextType {
  theme: ExtendedThemeConfig;
  setMode: (mode: 'light' | 'dark') => void;
  setDirection: (direction: 'ltr' | 'rtl') => void;
  toggleMode: () => void;
  toggleDirection: () => void;
  isDark: boolean;
  isRTL: boolean;
}

const RDSThemeContext = createContext<RDSThemeContextType | undefined>(undefined);

export function RDSThemeProvider({ children }: { children: React.ReactNode }) {
  const emotionTheme = useEmotionTheme() as ExtendedThemeConfig;
  const [theme, setTheme] = useState<ExtendedThemeConfig>(emotionTheme);

  // Update theme when emotion theme changes
  useEffect(() => {
    setTheme(emotionTheme);
  }, [emotionTheme]);

  // Apply theme changes to document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme.mode);
    document.documentElement.setAttribute('data-direction', theme.direction);
    document.documentElement.dir = theme.direction;
  }, [theme.mode, theme.direction]);

  const setMode = (mode: 'light' | 'dark') => {
    setTheme(prev => ({ ...prev, mode }));
  };

  const setDirection = (direction: 'ltr' | 'rtl') => {
    setTheme(prev => ({ ...prev, direction }));
  };

  const toggleMode = () => {
    setMode(theme.mode === 'light' ? 'dark' : 'light');
  };

  const toggleDirection = () => {
    setDirection(theme.direction === 'ltr' ? 'rtl' : 'ltr');
  };

  const isDark = theme.mode === 'dark';
  const isRTL = theme.direction === 'rtl';

  const value: RDSThemeContextType = {
    theme,
    setMode,
    setDirection,
    toggleMode,
    toggleDirection,
    isDark,
    isRTL,
  };

  return (
    <RDSThemeContext.Provider value={value}>
      {children}
    </RDSThemeContext.Provider>
  );
}

export function useRDSTheme(): RDSThemeContextType {
  const context = useContext(RDSThemeContext);
  if (context === undefined) {
    throw new Error('useRDSTheme must be used within a RDSThemeProvider');
  }
  return context;
}

// Utility hooks
export function useRDSColors() {
  const { theme } = useRDSTheme();
  return theme.rdsTheme;
}

export function useDirection() {
  const { theme, isRTL, toggleDirection } = useRDSTheme();
  return {
    direction: theme.direction,
    isRTL,
    isLTR: !isRTL,
    toggleDirection,
  };
}

export function useThemeMode() {
  const { theme, isDark, toggleMode } = useRDSTheme();
  return {
    mode: theme.mode,
    isDark,
    isLight: !isDark,
    toggleMode,
  };
}
