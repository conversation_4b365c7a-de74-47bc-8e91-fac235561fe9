# RDSTheme Documentation

RDSTheme integration using the official `@roshn/ui-kit` package with Emotion CSS for styling. This provides access to the Roshn Design System tokens, theme management, and utility functions directly from the UI kit.

## Features

- 🎨 **Official RDS Tokens**: Direct access to Roshn Design System tokens from `@roshn/ui-kit`
- 🌙 **Dark Mode**: Built-in dark mode support with automatic system preference detection
- 🌍 **Direction Support**: Built-in RTL/LTR direction support with LTR as default
- 🎯 **Type Safety**: Full TypeScript support with RoshnTheme types
- 💅 **Emotion CSS**: Powerful CSS-in-JS styling with Emotion
- 🎭 **Theme Provider**: React context-based theme management
- 🛠️ **RDS Utilities**: Helper functions for accessing RDS theme values

## Quick Start

### 1. Basic Usage with Emotion CSS

```tsx
import { css } from '@emotion/css';
import { useTheme, themeStyles, modeStyles } from '~/theme';

function MyComponent() {
  const { theme, isDark, toggleTheme } = useTheme();
  const styles = themeStyles(theme);

  const containerStyle = css`
    background-color: ${styles.colors.primary(600)};
    color: white;
    padding: ${styles.spacing(4)};
  `;

  const titleStyle = css`
    color: ${modeStyles(theme, styles.colors.secondary(900), 'white')};
    font-size: ${styles.fontSize('2xl')};
    font-weight: ${styles.fontWeight('bold')};
  `;

  return (
    <div className={containerStyle}>
      <h1 className={titleStyle}>Hello World with Emotion!</h1>
    </div>
  );
}
```

### 2. Using Theme Utilities

```tsx
import { css } from '@emotion/css';
import { useTheme, themeStyles, buttonStyles } from '~/theme';

function MyComponent() {
  const { theme, toggleTheme, isDark } = useTheme();
  const styles = themeStyles(theme);
  const buttons = buttonStyles(theme);

  return (
    <div>
      <button
        className={css`
          ${buttons.base}
          ${buttons.sizes.md}
          ${buttons.variants.primary}
        `}
        onClick={toggleTheme}
      >
        Switch to {isDark ? 'light' : 'dark'} mode
      </button>
      <p css={css`
        color: ${styles.colors.secondary(600)};
        margin-top: ${styles.spacing(4)};
      `}>
        Theme utilities make styling easy!
      </p>
    </div>
  );
}
```

### 3. Available Color Scales

- `primary`: Blue color scale (main brand color)
- `secondary`: Gray color scale (neutral colors)
- `success`: Green color scale
- `warning`: Yellow/Orange color scale
- `error`: Red color scale
- `neutral`: Additional gray scale

Each color scale has shades from 50 (lightest) to 900 (darkest).

### 4. Typography

```tsx
// Font families
className="font-sans" // Inter font
className="font-serif" // Serif fallback
className="font-mono" // Monospace

// Font sizes (with automatic line heights)
className="text-xs text-sm text-base text-lg text-xl text-2xl text-3xl text-4xl text-5xl text-6xl"
```

### 5. Spacing

Use the predefined spacing scale:

```tsx
className="p-4 m-6 gap-8" // Uses theme spacing tokens
```

### 6. Component Styles

Pre-built component styles are available:

```tsx
import { buttonStyles, cardStyles, inputStyles } from '~/theme';

// Use in className
<button className={`${buttonStyles.base} ${buttonStyles.sizes.md} ${buttonStyles.variants.primary}`}>
  Click me
</button>
```

## Theme Structure

### Colors
- Each color has 10 shades (50-900)
- Designed for both light and dark modes
- Accessible contrast ratios

### Typography
- Font families: sans, serif, mono
- Font sizes: xs to 6xl with appropriate line heights
- Font weights: thin to black

### Spacing
- Consistent spacing scale from 0 to 64
- Based on 0.25rem increments

### Border Radius
- From none to full (circle)
- Consistent with modern design trends

### Shadows
- 6 shadow levels: sm, base, md, lg, xl, 2xl
- Subtle and modern appearance

### Direction
- Built-in RTL/LTR support with LTR as default
- Automatic document direction application
- Direction-aware utility classes

## Direction Usage

### Basic Direction Control

```tsx
import { useTheme, useDirection } from '~/theme';

function MyComponent() {
  const { isRTL, toggleDirection } = useTheme();
  const { direction, isRTL: directionIsRTL, isLTR } = useDirection();

  return (
    <div>
      <p>Current direction: {direction}</p>
      <button onClick={toggleDirection}>
        Switch to {isRTL ? 'LTR' : 'RTL'}
      </button>
    </div>
  );
}
```

### Direction-Aware Styling with Emotion

```tsx
import { css } from '@emotion/css';
import { useTheme, themeStyles, directionStyles, themeDirectionStyles } from '~/theme';

function DirectionAwareComponent() {
  const { theme } = useTheme();
  const styles = themeStyles(theme);

  // Simple direction-aware styling
  const marginStyle = css`
    ${directionStyles(theme,
      `margin-left: ${styles.spacing(4)};`,
      `margin-right: ${styles.spacing(4)};`
    )}
  `;

  // Combined theme and direction styling
  const complexStyle = css`
    ${themeDirectionStyles(theme,
      `background-color: white; text-align: left;`,     // light LTR
      `background-color: white; text-align: right;`,    // light RTL
      `background-color: ${styles.colors.neutral(900)}; text-align: left;`,  // dark LTR
      `background-color: ${styles.colors.neutral(900)}; text-align: right;`  // dark RTL
    )}
    padding: ${styles.spacing(4)};
    border-radius: ${styles.borderRadius('lg')};
  `;

  return (
    <div>
      <div className={marginStyle}>
        Content with direction-aware margin
      </div>
      <div className={complexStyle}>
        Complex direction and theme styling
      </div>
    </div>
  );
}
```

### CSS Variables

Direction is available as a CSS custom property:

```css
/* Available direction variable */
--direction: ltr; /* or rtl */

/* Usage in CSS */
.my-element {
  margin-left: var(--direction) === 'ltr' ? 1rem : 0;
  margin-right: var(--direction) === 'rtl' ? 1rem : 0;
}
```

## Advanced Usage

### Custom CSS Variables

The theme automatically generates CSS custom properties:

```css
/* Available CSS variables */
--color-primary-600
--spacing-4
--font-size-lg
--border-radius-md
--shadow-lg
```

### Programmatic Theme Access

```tsx
import { getColor, getSpacing, getFontSize } from '~/theme';

const primaryColor = getColor(theme, 'primary', 600);
const mediumSpacing = getSpacing(theme, '4');
```

### Theme Customization

To customize the theme, modify `app/theme/tokens.ts`:

```tsx
export const tokens = {
  colors: {
    primary: {
      // Your custom primary colors
      500: '#your-color',
      600: '#your-darker-color',
    },
    // ... other customizations
  },
};
```

## Best Practices

1. **Use semantic color names**: Prefer `primary`, `secondary` over specific color names
2. **Consistent spacing**: Use the spacing scale for margins, padding, and gaps
3. **Responsive design**: Leverage the breakpoint tokens for consistent responsive behavior
4. **Dark mode**: Always consider both light and dark mode when using colors
5. **Direction awareness**: Consider RTL layouts when using directional properties (margins, padding, text alignment)
6. **Accessibility**: The theme includes accessible color contrasts by default

## Components

### ThemeToggle

A pre-built component for switching between light and dark modes:

```tsx
import { ThemeToggle } from '~/components/ThemeToggle';

<ThemeToggle />
```

### DirectionToggle

A pre-built component for switching between LTR and RTL directions:

```tsx
import { DirectionToggle } from '~/components/DirectionToggle';

<DirectionToggle />
```

## Migration Guide

### From Tailwind to Emotion CSS

If you're migrating from Tailwind CSS to Emotion CSS with RDSTheme:

1. **Install Emotion dependencies**:
   ```bash
   npm install @emotion/react @emotion/styled @emotion/css
   npm uninstall tailwindcss autoprefixer postcss
   ```

2. **Replace Tailwind classes with Emotion CSS**:
   ```tsx
   // Before (Tailwind)
   <div className="bg-primary-600 text-white p-4 rounded-lg">
     Content
   </div>

   // After (Emotion)
   import { css } from '@emotion/css';
   import { useTheme, themeStyles } from '~/theme';

   function Component() {
     const { theme } = useTheme();
     const styles = themeStyles(theme);

     return (
       <div css={css`
         background-color: ${styles.colors.primary(600)};
         color: white;
         padding: ${styles.spacing(4)};
         border-radius: ${styles.borderRadius('lg')};
       `}>
         Content
       </div>
     );
   }
   ```

3. **Use theme utilities for consistent styling**:
   ```tsx
   // Use buttonStyles, cardStyles, inputStyles functions
   const { theme } = useTheme();
   const buttons = buttonStyles(theme);

   <button className={css`${buttons.base} ${buttons.variants.primary}`}>
     Click me
   </button>
   ```

4. **Leverage direction and theme awareness**:
   ```tsx
   // Automatic theme and direction support
   const style = css`
     ${modeStyles(theme, 'color: black;', 'color: white;')}
     ${directionStyles(theme, 'text-align: left;', 'text-align: right;')}
   `;
   ```

The Emotion CSS setup provides more powerful styling capabilities while maintaining the same design tokens and theme structure.
