/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import type { ExtendedThemeConfig } from './RDSThemeProvider';

// Helper function to get theme values safely
export const getThemeValue = (theme: any, path: string, fallback?: any) => {
  const keys = path.split('.');
  let value = theme;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return fallback;
    }
  }
  
  return value || fallback;
};

// Direction utilities
export const directionStyles = (theme: ExtendedThemeConfig, ltrStyles: any, rtlStyles: any) => 
  theme.direction === 'rtl' ? rtlStyles : ltrStyles;

// Mode utilities  
export const modeStyles = (theme: ExtendedThemeConfig, lightStyles: any, darkStyles: any) =>
  theme.mode === 'dark' ? darkStyles : lightStyles;

// Combined theme and direction styles
export const themeDirectionStyles = (
  theme: ExtendedThemeConfig,
  lightLtr: any,
  lightRtl: any,
  darkLtr: any,
  darkRtl: any
) => {
  if (theme.mode === 'dark') {
    return theme.direction === 'rtl' ? darkRtl : darkLtr;
  }
  return theme.direction === 'rtl' ? lightRtl : lightLtr;
};

// RDS Theme utilities
export const rdsThemeStyles = (theme: ExtendedThemeConfig) => {
  const rds = theme.rdsTheme;
  
  return {
    // Color utilities - try to access RDS colors with fallbacks
    colors: {
      primary: (shade?: string | number) => getThemeValue(rds, `colors.primary.${shade || '500'}`, '#3B82F6'),
      secondary: (shade?: string | number) => getThemeValue(rds, `colors.secondary.${shade || '500'}`, '#6B7280'),
      success: (shade?: string | number) => getThemeValue(rds, `colors.success.${shade || '500'}`, '#10B981'),
      warning: (shade?: string | number) => getThemeValue(rds, `colors.warning.${shade || '500'}`, '#F59E0B'),
      error: (shade?: string | number) => getThemeValue(rds, `colors.error.${shade || '500'}`, '#EF4444'),
      neutral: (shade?: string | number) => getThemeValue(rds, `colors.neutral.${shade || '500'}`, '#6B7280'),
    },
    
    // Spacing utilities
    spacing: (size: string | number) => getThemeValue(rds, `spacing.${size}`, `${size}rem`),
    
    // Typography utilities
    fontSize: (size: string) => getThemeValue(rds, `fontSize.${size}`, '1rem'),
    fontWeight: (weight: string) => getThemeValue(rds, `fontWeight.${weight}`, '400'),
    lineHeight: (height: string) => getThemeValue(rds, `lineHeight.${height}`, '1.5'),
    
    // Border radius utilities
    borderRadius: (radius: string) => getThemeValue(rds, `borderRadius.${radius}`, '0.25rem'),
    
    // Shadow utilities
    shadow: (shadowSize: string) => getThemeValue(rds, `shadows.${shadowSize}`, 'none'),
    
    // Direction utilities
    direction: theme.direction,
    isRTL: theme.direction === 'rtl',
    isLTR: theme.direction === 'ltr',
    
    // Mode utilities
    mode: theme.mode,
    isDark: theme.mode === 'dark',
    isLight: theme.mode === 'light',
  };
};

// Button styles using RDS theme
export const rdsButtonStyles = (theme: ExtendedThemeConfig) => {
  const styles = rdsThemeStyles(theme);
  
  return {
    base: css`
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      transition: all 0.2s ease-in-out;
      border: none;
      cursor: pointer;
      text-decoration: none;
      border-radius: ${styles.borderRadius('md')};
      
      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px ${styles.colors.primary()}40;
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    `,
    
    sizes: {
      sm: css`
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      `,
      md: css`
        padding: 0.625rem 1rem;
        font-size: 1rem;
      `,
      lg: css`
        padding: 0.75rem 1.5rem;
        font-size: 1.125rem;
      `,
    },
    
    variants: {
      primary: css`
        background-color: ${styles.colors.primary()};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${styles.colors.primary('600')};
        }
      `,
      secondary: css`
        background-color: ${modeStyles(theme, styles.colors.secondary('200'), styles.colors.secondary('700'))};
        color: ${modeStyles(theme, styles.colors.secondary('900'), styles.colors.secondary('100'))};
        
        &:hover:not(:disabled) {
          background-color: ${modeStyles(theme, styles.colors.secondary('300'), styles.colors.secondary('600'))};
        }
      `,
      outline: css`
        background-color: transparent;
        border: 1px solid ${modeStyles(theme, styles.colors.secondary('300'), styles.colors.secondary('600'))};
        color: ${modeStyles(theme, styles.colors.secondary('700'), styles.colors.secondary('300'))};
        
        &:hover:not(:disabled) {
          background-color: ${modeStyles(theme, styles.colors.secondary('50'), styles.colors.secondary('800'))};
        }
      `,
    },
  };
};

// Card styles using RDS theme
export const rdsCardStyles = (theme: ExtendedThemeConfig) => {
  const styles = rdsThemeStyles(theme);
  
  return {
    base: css`
      background-color: ${modeStyles(theme, 'white', styles.colors.secondary('800'))};
      border: 1px solid ${modeStyles(theme, styles.colors.secondary('200'), styles.colors.secondary('700'))};
      border-radius: ${styles.borderRadius('lg')};
      box-shadow: ${modeStyles(theme, '0 1px 3px 0 rgba(0, 0, 0, 0.1)', '0 4px 6px -1px rgba(0, 0, 0, 0.1)')};
      transition: box-shadow 0.2s ease-in-out;
      
      &:hover {
        box-shadow: ${modeStyles(theme, '0 4px 6px -1px rgba(0, 0, 0, 0.1)', '0 10px 15px -3px rgba(0, 0, 0, 0.1)')};
      }
    `,
    
    padding: {
      sm: css`padding: 1rem;`,
      md: css`padding: 1.5rem;`,
      lg: css`padding: 2rem;`,
    },
  };
};

// Input styles using RDS theme
export const rdsInputStyles = (theme: ExtendedThemeConfig) => {
  const styles = rdsThemeStyles(theme);
  
  return {
    base: css`
      display: block;
      width: 100%;
      border: 1px solid ${modeStyles(theme, styles.colors.secondary('300'), styles.colors.secondary('600'))};
      background-color: ${modeStyles(theme, 'white', styles.colors.secondary('800'))};
      color: ${modeStyles(theme, styles.colors.secondary('900'), styles.colors.secondary('100'))};
      border-radius: ${styles.borderRadius('md')};
      padding: 0.625rem 0.75rem;
      font-size: 1rem;
      transition: all 0.2s ease-in-out;
      
      &::placeholder {
        color: ${modeStyles(theme, styles.colors.secondary('500'), styles.colors.secondary('400'))};
      }
      
      &:focus {
        outline: none;
        border-color: ${styles.colors.primary()};
        box-shadow: 0 0 0 2px ${styles.colors.primary()}40;
      }
      
      &:disabled {
        background-color: ${modeStyles(theme, styles.colors.secondary('50'), styles.colors.secondary('900'))};
        color: ${modeStyles(theme, styles.colors.secondary('500'), styles.colors.secondary('400'))};
        cursor: not-allowed;
      }
    `,
    
    variants: {
      default: css``,
      error: css`
        border-color: ${modeStyles(theme, styles.colors.error('300'), styles.colors.error('600'))};
        color: ${modeStyles(theme, styles.colors.error('900'), styles.colors.error('100'))};
        
        &:focus {
          border-color: ${styles.colors.error()};
          box-shadow: 0 0 0 2px ${styles.colors.error()}40;
        }
      `,
    },
  };
};

// Animation utilities
export const animations = {
  fadeIn: css`
    opacity: 0;
    transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
    
    &.visible {
      opacity: 1;
    }
  `,
  slideUp: css`
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  `,
  slideRight: css`
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.visible {
      opacity: 1;
      transform: translateX(0);
    }
  `,
};

// Responsive utilities
export const breakpoints = {
  sm: '@media (min-width: 640px)',
  md: '@media (min-width: 768px)',
  lg: '@media (min-width: 1024px)',
  xl: '@media (min-width: 1280px)',
  '2xl': '@media (min-width: 1536px)',
};
